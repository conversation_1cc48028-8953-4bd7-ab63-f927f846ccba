<ui:composition
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
  xmlns:h="http://xmlns.jcp.org/jsf/html"
  xmlns:p="http://primefaces.org/ui"
>
  <h:form>
    <h2>Relatório de Concomitância de Remuneração por CPF</h2>
    <p:dataTable
      value="#{relatorioConcomitanciaRemuneracaoBean.lista}"
      var="item"
      paginator="true"
      rows="20"
      emptyMessage="Nenhum registro encontrado."
    >
      <p:column headerText="CPF">
        <h:outputText value="#{item.cpf}" />
      </p:column>
      <p:column headerText="Nome do Servidor">
        <h:outputText value="#{item.nomeServidor}" />
      </p:column>
      <p:column headerText="Período de Concomitância">
        <h:outputText value="#{item.periodoConcomitancia}" />
      </p:column>
      <p:column headerText="Cargos Acumulados">
        <h:outputText value="#{item.cargosAcumulados}" />
      </p:column>
      <p:column headerText="Nº Meses Simultâneos">
        <h:outputText value="#{item.numMesesSimultaneos}" />
      </p:column>
      <p:column headerText="Total Recebido Concomitante">
        <h:outputText value="#{item.totalRecebidoConcomitante}">
          <f:convertNumber type="currency" currencySymbol="R$" />
        </h:outputText>
      </p:column>
    </p:dataTable>
    
    <p:commandButton
      value="Exportar PDF"
      icon="pi pi-file-pdf"
      ajax="false"
      actionListener="#{relatorioConcomitanciaRemuneracaoBean.exportarPdf}"
      onclick="PrimeFaces.monitorDownload(start, stop);"
      styleClass="ui-button-success"
    >
      <p:fileDownload
        value="#{relatorioConcomitanciaRemuneracaoBean.exportarPdf()}"
      />
    </p:commandButton>
    
  </h:form>
</ui:composition>
