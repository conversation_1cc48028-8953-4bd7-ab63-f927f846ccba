package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import br.gov.ac.tce.sicapweb.repositorio.RemessaPeriodicaRepositorio;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;

@Named
@ViewScoped
public class ExportarDados implements Serializable {

	private static final long serialVersionUID = 1L;

//	@Inject
//	private LoginBean loginBean;

//	@Inject
//	private Entidade entidade;
	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private RemessaPeriodicaRepositorio remessaPeriodicaRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;

	private Collection<Integer> listaExercicios;
	private Collection<Entidade> listaEntidade;
	private List<Entidade> entidadesSelecionadas;

	private String[] anosSelecionados;

	@PostConstruct
	public void init() {
		try {
			this.entidadesSelecionadas = new ArrayList<Entidade>();
			this.listaExercicios = this.remessaPeriodicaRepositorio.listaExercicio();
			this.listaEntidade = this.entidadeRepositorio.listaTodasRepositorioSicap();
		} catch (RepositorioException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void teste() {
		List<String> anos = new LinkedList<>();
		List<String> idEntidades = new LinkedList<>();
		String parametroAno = null;
		String parametroEntidades = null;

		for (String a : this.anosSelecionados) {
			anos.add(a);
		}

		if (anos.size() != 0) {
			parametroAno = String.join(", ", anos);
		}

		for (Entidade e : this.entidadesSelecionadas) {
			idEntidades.add(e.getIdEntidadeCjur().toString());
		}

		if (idEntidades.size() != 0) {
			parametroEntidades = String.join(", ", idEntidades);
		}

		System.out.println("ano: " + parametroAno);
		System.out.println("idEntidade: " + parametroEntidades);
	}

	public void exportarDadosAnualEntidade() {

		List<String> anos = new LinkedList<>();
		List<String> idEntidades = new LinkedList<>();
		String parametroAno = null;
		String parametroEntidades = null;

		// Validação inicial
		if (this.anosSelecionados == null || this.anosSelecionados.length == 0) {
			System.err.println("ERRO: Nenhum ano foi selecionado!");
			throw new RuntimeException("É necessário selecionar pelo menos um ano para exportação.");
		}

		if (this.entidadesSelecionadas == null || this.entidadesSelecionadas.size() == 0) {
			System.err.println("ERRO: Nenhuma entidade foi selecionada!");
			throw new RuntimeException("É necessário selecionar pelo menos uma entidade para exportação.");
		}

		for (String a : this.anosSelecionados) {
			anos.add(a);
		}

		if (anos.size() != 0) {
			parametroAno = String.join(", ", anos);
		}

		for (Entidade e : this.entidadesSelecionadas) {
			idEntidades.add(e.getIdEntidadeCjur().toString());
		}

		if (idEntidades.size() != 0) {
			parametroEntidades = String.join(", ", idEntidades);
		}

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("idEntidades", parametroEntidades);
		parameters.put("ano", parametroAno);

		// Log dos parâmetros para debug
		System.out.println("=== DEBUG EXPORTAR DADOS ===");
		System.out.println("Parâmetro idEntidades: " + parametroEntidades);
		System.out.println("Parâmetro ano: " + parametroAno);
		System.out.println("Entidades selecionadas: " + this.entidadesSelecionadas.size());
		System.out.println("Anos selecionados: " + this.anosSelecionados.length);

		JasperPrint jasperPrint;
		try(Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/") + "/";
			String pathRelatorio = pathRelatorioDir + "exportarDados/" + "exportar_dados_anual_entidade.jasper";

			System.out.println("Caminho do relatório: " + pathRelatorio);
			System.out.println("Arquivo existe: " + new java.io.File(pathRelatorio).exists());

			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
			httpServletResponse.setContentType("application/vnd.ms-excel");
			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "dadosEntidade.xlsx");

			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters, conexaoJasper);

			System.out.println("JasperPrint criado com sucesso");
			System.out.println("Número de páginas: " + jasperPrint.getPages().size());
			if (jasperPrint.getPages().size() > 0) {
				System.out.println("Elementos na primeira página: " + jasperPrint.getPages().get(0).getElements().size());
			}

			JRXlsxExporter xlsxExporter = new JRXlsxExporter();
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

			// Usar API moderna do JasperReports
			xlsxExporter.setExporterInput(new net.sf.jasperreports.export.SimpleExporterInput(jasperPrint));
			xlsxExporter.setExporterOutput(new net.sf.jasperreports.export.SimpleOutputStreamExporterOutput(byteArrayOutputStream));

			// Configurações específicas para Excel
			net.sf.jasperreports.export.SimpleXlsxReportConfiguration reportConfig = new net.sf.jasperreports.export.SimpleXlsxReportConfiguration();
			reportConfig.setOnePagePerSheet(false);
			reportConfig.setRemoveEmptySpaceBetweenColumns(true);
			reportConfig.setRemoveEmptySpaceBetweenRows(true);
			reportConfig.setDetectCellType(true);
			reportConfig.setCollapseRowSpan(true);
			reportConfig.setIgnoreGraphics(false);
			reportConfig.setIgnoreCellBorder(false);
			reportConfig.setWhitePageBackground(false);
			xlsxExporter.setConfiguration(reportConfig);

			System.out.println("Iniciando exportação Excel...");
			xlsxExporter.exportReport();
			System.out.println("Exportação concluída");

			byte[] excelBytes = byteArrayOutputStream.toByteArray();
			System.out.println("Tamanho do arquivo Excel gerado: " + excelBytes.length + " bytes");

			if (excelBytes.length < 1000) {
				System.err.println("AVISO: Arquivo Excel muito pequeno, pode estar vazio!");
			}

			httpServletResponse.getOutputStream().write(excelBytes);
			httpServletResponse.getOutputStream().flush();
			httpServletResponse.getOutputStream().close();
			httpServletResponse.flushBuffer();

			FacesContext.getCurrentInstance().responseComplete();
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println("Erro ao gerar relatório Excel: " + e.getMessage());
			System.err.println("Tipo de erro: " + e.getClass().getSimpleName());
			if (e.getCause() != null) {
				System.err.println("Causa raiz: " + e.getCause().getMessage());
			}
			// Rethrow para que o erro seja visível ao usuário
			throw new RuntimeException("Erro ao gerar relatório Excel: " + e.getMessage(), e);
		}
	}

	public String[] getAnosSelecionados() {
		return anosSelecionados;
	}

	public void setAnosSelecionados(String[] anosSelecionados) {
		this.anosSelecionados = anosSelecionados;
	}

	public Collection<Integer> getListaExercicios() {
		return listaExercicios;
	}

	public void setListaExercicios(Collection<Integer> listaExercicios) {
		this.listaExercicios = listaExercicios;
	}

	public Collection<Entidade> getListaEntidade() {
		return listaEntidade;
	}

	public void setListaEntidade(Collection<Entidade> listaEntidade) {
		this.listaEntidade = listaEntidade;
	}

	public List<Entidade> getEntidadesSelecionadas() {
		return entidadesSelecionadas;
	}

	public void setEntidadesSelecionadas(List<Entidade> entidadesSelecionadas) {
		this.entidadesSelecionadas = entidadesSelecionadas;
	}

}
