package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;

import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named("acumulacaoCpfBean")
@ViewScoped
public class AcumulacaoCpfBean implements Serializable {

    private static final long serialVersionUID = 1L;


    @Inject
    private AcumulacaoBusiness acumulacaoBusiness;

    private String cpf;
    private transient Collection<Object[]> listaResumoConcomitancia;



    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Collection<Object[]> getListaResumoConcomitancia() {
        return listaResumoConcomitancia;
    }

    public void pesquisar() {
    System.out.println("[DEBUG] Chamando pesquisar() para CPF: " + this.cpf);
    try {
        String cpfLimpo = this.cpf != null ? this.cpf.replaceAll("[^0-9]", "") : null;
        this.listaResumoConcomitancia = acumulacaoBusiness.resumoConcomitanciaPorCpf(cpfLimpo);
        System.out.println("[DEBUG] Resultado: " + (listaResumoConcomitancia != null ? listaResumoConcomitancia.size() : "null"));
    } catch (RepositorioException e) {
        e.printStackTrace();
        Mensagem.setMensagem(MensagemType.ERRO, "Erro ao buscar resumo de concomitância.", "");
    }
}
}
