package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import javax.faces.view.ViewScoped;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.JasperExportManager;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.ServletOutputStream;

@Named
@ViewScoped
public class RelatorioConcomitanciaRemuneracaoBean implements Serializable {
    private List<ConcomitanciaDTO> lista;

    public RelatorioConcomitanciaRemuneracaoBean() {
        lista = new ArrayList<>();
        //
        lista.add(new ConcomitanciaDTO("123.456.789-00", "<PERSON>", "01/2024 - 03/2024", "Professor, Diretor", 3, new BigDecimal("15000.00")));
    }

    public List<ConcomitanciaDTO> getLista() {
        return lista;
    }

    public void exportarPdf() {
        try {
            Map<String, Object> parametros = new HashMap<>();
            parametros.put("REPORT_TITLE", "Relatório de Concomitância de Remuneração por CPF");
            JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(lista);
            String caminhoJasper = "/relatorios/acumulacao/relatorioConcomitanciaRemuneracao.jasper";
            JasperPrint jasperPrint = JasperFillManager.fillReport(
                getClass().getResourceAsStream(caminhoJasper), parametros, dataSource);

            FacesContext facesContext = FacesContext.getCurrentInstance();
            HttpServletResponse response = (HttpServletResponse) facesContext.getExternalContext().getResponse();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=relatorioConcomitanciaRemuneracao.pdf");
            ServletOutputStream out = response.getOutputStream();
            JasperExportManager.exportReportToPdfStream(jasperPrint, out);
            out.flush();
            out.close();
            facesContext.responseComplete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static class ConcomitanciaDTO implements Serializable {
        private String cpf;
        private String nomeServidor;
        private String periodoConcomitancia;
        private String cargosAcumulados;
        private Integer numMesesSimultaneos;
        private BigDecimal totalRecebidoConcomitante;

        public ConcomitanciaDTO(String cpf, String nomeServidor, String periodoConcomitancia, String cargosAcumulados, Integer numMesesSimultaneos, BigDecimal totalRecebidoConcomitante) {
            this.cpf = cpf;
            this.nomeServidor = nomeServidor;
            this.periodoConcomitancia = periodoConcomitancia;
            this.cargosAcumulados = cargosAcumulados;
            this.numMesesSimultaneos = numMesesSimultaneos;
            this.totalRecebidoConcomitante = totalRecebidoConcomitante;
        }
        public String getCpf() { return cpf; }
        public void setCpf(String cpf) { this.cpf = cpf; }
        public String getNomeServidor() { return nomeServidor; }
        public void setNomeServidor(String nomeServidor) { this.nomeServidor = nomeServidor; }
        public String getPeriodoConcomitancia() { return periodoConcomitancia; }
        public void setPeriodoConcomitancia(String periodoConcomitancia) { this.periodoConcomitancia = periodoConcomitancia; }
        public String getCargosAcumulados() { return cargosAcumulados; }
        public void setCargosAcumulados(String cargosAcumulados) { this.cargosAcumulados = cargosAcumulados; }
        public Integer getNumMesesSimultaneos() { return numMesesSimultaneos; }
        public void setNumMesesSimultaneos(Integer numMesesSimultaneos) { this.numMesesSimultaneos = numMesesSimultaneos; }
        public BigDecimal getTotalRecebidoConcomitante() { return totalRecebidoConcomitante; }
        public void setTotalRecebidoConcomitante(BigDecimal totalRecebidoConcomitante) { this.totalRecebidoConcomitante = totalRecebidoConcomitante; }
    }
}
